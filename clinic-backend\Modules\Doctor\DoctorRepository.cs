using Modules.Doctor.DAO;
using Data;
using Microsoft.EntityFrameworkCore;

namespace Modules.Doctor.Repositories
{
    public class DoctorRepository : IDoctorRepository
    {
        private readonly ClinicDbContext _context;

        public DoctorRepository(ClinicDbContext context)
        {
            _context = context;
        }

        public List<DoctorDAO> GetAll()
        {
            return _context.Doctors
                .AsNoTracking()
                .Select(d => DoctorMapper.ToDAO(d))
                .ToList();
        }

        public DoctorDAO GetById(int id)
        {
            var d = _context.Doctors.AsNoTracking().FirstOrDefault(doc => doc.Id == id);
            if (d == null)
                throw new KeyNotFoundException($"Doctor with ID {id} not found.");
            return DoctorMapper.ToDAO(d);
        }

        public void Add(DoctorDAO dao)
        {
            if (dao == null)
                throw new ArgumentNullException(nameof(dao));
            var entity = DoctorMapper.ToEntity(dao);
            _context.Doctors.Add(entity);
            _context.SaveChanges();
        }

        public void Update(DoctorDAO dao)
        {
            if (dao == null)
                throw new ArgumentNullException(nameof(dao));

            var existing = _context.Doctors.Find(dao.Id);
            if (existing == null)
                throw new KeyNotFoundException($"Doctor with ID {dao.Id} not found.");

            DoctorMapper.MapToExistingEntity(dao, existing,
            DoctorMapper.GetDepartment(dao));
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var doctor = _context.Doctors.Find(id);
            if (doctor == null)
                throw new KeyNotFoundException($"Doctor with ID {id} not found.");

            _context.Doctors.Remove(doctor);
            _context.SaveChanges();
        }

        public bool Exists(int id)
        {
            return _context.Doctors.Any(d => d.Id == id);
        }
    }
}
