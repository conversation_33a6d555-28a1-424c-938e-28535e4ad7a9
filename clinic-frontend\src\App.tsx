import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import ReactPlugin from '@stagewise-plugins/react'

function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
    </>
  )
}

export default App
