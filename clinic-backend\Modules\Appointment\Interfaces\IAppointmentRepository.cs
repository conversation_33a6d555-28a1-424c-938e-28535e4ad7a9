
using Modules.Appointment.DAO;

namespace Modules.Appointment.Interfaces
{
    public interface IAppointmentRepository
    {
        void Create(AppointmentDAO appointment);
        void Update(AppointmentDAO appointment);
        void Delete(int id);
        IEnumerable<AppointmentDAO> GetAll();
        AppointmentDAO? GetById(int id);
        AppointmentDAO? GetByPatientName(string patientName);
        AppointmentDAO? GetByDoctorName(string doctorName);
        IEnumerable<AppointmentDAO> GetByDateRange(DateTime startDate, DateTime endDate);
    }
}
