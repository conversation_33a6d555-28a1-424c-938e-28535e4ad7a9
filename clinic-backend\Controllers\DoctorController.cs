using Microsoft.AspNetCore.Mvc;
using Modules.Doctor.DTOs;
using Modules.Doctor.Services;
using Modules.Doctor.Repositories;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DoctorController : ControllerBase
    {
        private readonly IDoctorService _service;

        public DoctorController(IDoctorService service)
        {
            _service = service;
        }

        [HttpGet]
        public ActionResult<IEnumerable<DoctorRespondDTO>> GetAll()
        {
            var doctors = _service.GetAllDoctors();
            return Ok(doctors);
        }

        [HttpGet("{id}")]
        public ActionResult<DoctorRespondDTO> GetById(int id)
        {
            try
            {
                var doctor = _service.GetDoctorById(id);
                return Ok(doctor);
            }
            catch
            {
                return NotFound(new { warning = $"Doctor with id {id} not found." });
            }
        }

        [HttpPost]
        public IActionResult Create([FromBody] DoctorCreateDTO dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Name) || string.IsNullOrWhiteSpace(dto.Specialty))
            {
                return BadRequest("Name and Specialty are required.");
            }
            _service.CreateDoctor(dto);
            return Ok();
        }

        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody] DoctorUpdateDTO dto)
        {
            try
            {
                _service.UpdateDoctor(dto);
                return Ok();
            }
            catch
            {
                return NotFound(new { warning = $"Doctor with id {id} not found." });
            }
        }

        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            try
            {
                _service.DeleteDoctor(id);
                return Ok();
            }
            catch
            {
                return NotFound(new { warning = $"Doctor with id {id} not found." });
            }
        }
    }
}