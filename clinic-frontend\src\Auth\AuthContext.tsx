import React, { createContext, useContext, useState, ReactNode } from 'react';
import type { User } from '../Types/index';

interface AuthContextType {
  user: User | null;
  isAdmin: boolean;
  isGuest: boolean;
  login: (email: string, password: string) => boolean;
  loginAsAdmin: (email: string, password: string) => boolean;
  logout: () => void;
  continueAsGuest: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isGuest, setIsGuest] = useState(false);

  const login = (email: string, password: string): boolean => {
    const foundUser = user.find(u => u.email === email);
    if (foundUser && password === 'password') {
      setUser(foundUser);
      setIsAdmin(false);
      setIsGuest(false);
      return true;
    }
    return false;
  };

  const loginAsAdmin = (email: string, password: string): boolean => {
    if (email === '<EMAIL>' && password === 'admin123') {
      setUser(null);
      setIsAdmin(true);
      setIsGuest(false);
      return true;
    }
    return false;
  };

  const logout = () => {
    setUser(null);
    setIsAdmin(false);
    setIsGuest(false);
  };

  const continueAsGuest = () => {
    setUser(null);
    setIsAdmin(false);
    setIsGuest(true);
  };

  return (
    <AuthContext.Provider value={{
      user,
      isAdmin,
      isGuest,
      login,
      loginAsAdmin,
      logout,
      continueAsGuest,
    }}>
      {children}
    </AuthContext.Provider>
  );
};