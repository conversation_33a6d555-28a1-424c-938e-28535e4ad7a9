using Modules.Enums;
namespace Modules.Doctor.DTOs
{
    public class DoctorUpdateDTO
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public required string Specialty { get; set; }
        public required string Phone { get; set; }
        public required string Email { get; set; }
        public string? Department { get; set; }
        public string? PhotoUrl { get; set; }
        public double Rating { get; set; }
        public string? Location { get; set; }
        public string? Experience { get; set; }
        public DoctorAvailability Availability { get; set; } = DoctorAvailability.Offline;
    }
}
