
using Modules.Patient.DTOs;
using Modules.Patient.DAO;
using Modules.Patient.Entities;

namespace Modules.Patient
{
    public static class PatientMapper
    {
        // ===================== DTOs =====================
        public static PatientDAO ToDAO(PatientCreateDTO dto)
        {
            if (dto == null) return null!;
            return new PatientDAO
            {
                Id = dto.Id,
                Name = dto.Name,
                DateOfBirth = dto.DateOfBirth,
                Phone = dto.Phone,
                Email = dto.Email,
                Age = dto.Age,
                Gender = dto.Gender
            };
        }

        public static PatientDAO ToDAO(PatientUpdateDTO dto)
        {
            if (dto == null) return null!;
            return new PatientDAO
            {
                Id = dto.Id,
                Name = dto.Name,
                DateOfBirth = dto.DateOfBirth,
                Phone = dto.Phone,
                Email = dto.Email,
                Age = dto.Age,
                Gender = dto.Gender
            };
        }

        public static PatientRespondDTO ToRespondDTO(PatientDAO dao)
        {
            if (dao == null) return null!;
            return new PatientRespondDTO
            {
                Id = dao.Id,
                Name = dao.Name,
                DateOfBirth = dao.DateOfBirth,
                Phone = dao.Phone,
                Email = dao.Email,
                Age = dao.Age,
                Gender = dao.Gender
            };
        }

        public static List<PatientRespondDTO> ToRespondDTOList(IEnumerable<PatientDAO> daos)
        {
            return daos?.Select(ToRespondDTO).ToList() ?? new List<PatientRespondDTO>();
        }

        // ===================== DAO <-> Entity =====================
        public static PatientEntity ToEntity(PatientDAO dao)
        {
            if (dao == null) return null!;
            return new PatientEntity
            {
                Id = dao.Id,
                Name = dao.Name,
                DateOfBirth = dao.DateOfBirth,
                Phone = dao.Phone,
                Email = dao.Email,
                Age = dao.Age,
                Gender = dao.Gender
            };
        }

        public static PatientDAO ToDAO(PatientEntity entity)
        {
            if (entity == null) return null!;
            return new PatientDAO
            {
                Id = entity.Id,
                Name = entity.Name,
                DateOfBirth = entity.DateOfBirth,
                Phone = entity.Phone,
                Email = entity.Email,
                Age = entity.Age,
                Gender = entity.Gender
            };
        }

        public static void MapToExistingEntity(PatientDAO dao, PatientEntity existing)
        {
            if (dao == null || existing == null) return;
            existing.Name = dao.Name;
            existing.DateOfBirth = dao.DateOfBirth;
            existing.Phone = dao.Phone;
            existing.Email = dao.Email;
            existing.Age = dao.Age;
            existing.Gender = dao.Gender;
        }

        // ===================== Entity -> DTO =====================
        public static PatientRespondDTO ToRespondDTO(PatientEntity entity)
        {
            if (entity == null) return null!;
            return new PatientRespondDTO
            {
                Id = entity.Id,
                Name = entity.Name,
                DateOfBirth = entity.DateOfBirth,
                Phone = entity.Phone,
                Email = entity.Email,
                Age = entity.Age,
                Gender = entity.Gender
            };
        }
    }
}
