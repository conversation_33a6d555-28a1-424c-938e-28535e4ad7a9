using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Modules.Doctor.Entities;

public class BookedSlot
{
    [Key]
    public int Id { get; set; }

    public DateTime Date { get; set; }

    public TimeSpan Time { get; set; }

    [<PERSON><PERSON><PERSON>("Doctor")]
    public string DoctorId { get; set; }

    public DoctorEntity Doctor { get; set; }
}