using System;
using System.Collections.Generic;
using Modules.Appointment.DTOs;

namespace Modules.Appointment.Services
{
    public interface IAppointmentService
    {
        void Create(AppointmentCreateDTO dto);
        void Update(AppointmentUpdateDTO dto);
        void Delete(int id);
        IEnumerable<AppointmentRespondDTO> GetAll();
        AppointmentRespondDTO? GetById(int id);
        IEnumerable<AppointmentRespondDTO> GetByDateRange(DateTime start, DateTime end);
    }
}