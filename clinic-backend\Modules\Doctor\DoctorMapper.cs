using Modules.Doctor.DTOs;
using Modules.Doctor.DAO;
using Modules.Doctor.Entities;

namespace Modules.Doctor
{
    public static class DoctorMapper
    {
        // ===================== DTOs =====================
        public static DoctorDAO ToDAO(DoctorCreateDTO dto)
        {
            if (dto == null) return null!;
            return new DoctorDAO
            {
                Id = dto.Id,
                Name = dto.Name,
                Specialty = dto.Specialty,
                Phone = dto.Phone,
                Email = dto.Email,
                Department = dto.Department,
                PhotoUrl = dto.PhotoUrl,
                Rating = dto.Rating,
                Location = dto.Location,
                Experience = dto.Experience,
                Availability = dto.Availability
            };
        }

        public static DoctorDAO ToDAO(DoctorUpdateDTO dto)
        {
            if (dto == null) return null!;
            return new DoctorDAO
            {
                Id = dto.Id,
                Name = dto.Name,
                Specialty = dto.Specialty,
                Phone = dto.Phone,
                Email = dto.Email,
                Department = dto.Department,
                PhotoUrl = dto.PhotoUrl,
                Rating = dto.Rating,
                Location = dto.Location,
                Experience = dto.Experience,
                Availability = dto.Availability
            };
        }

        public static DoctorRespondDTO ToRespondDTO(DoctorDAO dao)
        {
            if (dao == null) return null!;
            return new DoctorRespondDTO
            {
                Id = dao.Id,
                Name = dao.Name,
                Specialty = dao.Specialty,
                Phone = dao.Phone,
                Email = dao.Email,
                Department = dao.Department,
                PhotoUrl = dao.PhotoUrl,
                Rating = dao.Rating,
                Location = dao.Location,
                Experience = dao.Experience,
                Availability = dao.Availability
            };
        }

        public static List<DoctorRespondDTO> ToRespondDTOList(IEnumerable<DoctorDAO> daos)
        {
            return daos?.Select(ToRespondDTO).ToList() ?? new List<DoctorRespondDTO>();
        }

        // ===================== DAO <-> Entity =====================
        public static DoctorEntity ToEntity(DoctorDAO dao)
        {
            if (dao == null) return null!;
            return new DoctorEntity
            {
                Id = dao.Id,
                Name = dao.Name,
                Specialty = dao.Specialty,
                Phone = dao.Phone,
                Email = dao.Email,
                Department = dao.Department,
                PhotoUrl = dao.PhotoUrl,
                Rating = dao.Rating,
                Location = dao.Location,
                Experience = dao.Experience,
                Availability = dao.Availability
            };
        }

        public static DoctorDAO ToDAO(DoctorEntity entity)
        {
            if (entity == null) return null!;
            return new DoctorDAO
            {
                Id = entity.Id,
                Name = entity.Name,
                Specialty = entity.Specialty,
                Phone = entity.Phone,
                Email = entity.Email,
                Department = entity.Department,
                PhotoUrl = entity.PhotoUrl,
                Rating = entity.Rating,
                Location = entity.Location,
                Experience = entity.Experience,
                Availability = entity.Availability
            };
        }

        public static string? GetDepartment(DoctorDAO dao)
        {
            return dao.Department;
        }

        public static void MapToExistingEntity(DoctorDAO dao, DoctorEntity existing, string? department)
        {
            if (dao == null || existing == null) return;
            existing.Name = dao.Name;
            existing.Specialty = dao.Specialty;
            existing.Phone = dao.Phone;
            existing.Email = dao.Email;
            existing.Department = department;
            existing.PhotoUrl = dao.PhotoUrl;
            existing.Rating = dao.Rating;
            existing.Location = dao.Location;
            existing.Experience = dao.Experience;
            existing.Availability = dao.Availability;
        }

        // ===================== Entity -> DTO =====================
        public static DoctorRespondDTO ToRespondDTO(DoctorEntity entity)
        {
            if (entity == null) return null!;
            return new DoctorRespondDTO
            {
                Id = entity.Id,
                Name = entity.Name,
                Specialty = entity.Specialty,
                Phone = entity.Phone,
                Email = entity.Email,
                Department = entity.Department,
                PhotoUrl = entity.PhotoUrl,
                Rating = entity.Rating,
                Location = entity.Location,
                Experience = entity.Experience,
                Availability = entity.Availability
            };
        }
    }
}
