using Modules.Appointment.Entities;
using Modules.Appointment.Interfaces;
using Modules.Appointment.DAO;
using Modules.Appointment;
using Data;
using Microsoft.EntityFrameworkCore;

namespace Modules.Appointment.Repositories
{
    public class AppointmentRepository : IAppointmentRepository
    {
        private readonly ClinicDbContext _context;

        public AppointmentRepository(ClinicDbContext context)
        {
            _context = context;
        }

        public void Create(AppointmentDAO dao)
        {
            var entity = AppointmentMapper.ToEntity(dao);
            _context.Appointments.Add(entity);
            _context.SaveChanges();
        }

        public void Update(AppointmentDAO dao)
        {
            var existing = _context.Appointments.Find(dao.Id);
            if (existing == null)
                throw new KeyNotFoundException($"Appointment with ID {dao.Id} not found.");
            AppointmentMapper.MapToExistingEntity(dao, existing);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var appointment = _context.Appointments.Find(id);
            if (appointment != null)
            {
                _context.Appointments.Remove(appointment);
                _context.SaveChanges();
            }
            else
            {
                throw new KeyNotFoundException($"Appointment with ID {id} not found.");
            }
        }

        public IEnumerable<AppointmentDAO> GetAll()
        {
            return _context.Appointments
                .AsNoTracking()
                .Select(a => AppointmentMapper.ToDAO(a))
                .ToList();
        }

        public AppointmentDAO? GetById(int id)
        {
            var a = _context.Appointments.Find(id);
            if (a == null) return null;
            return AppointmentMapper.ToDAO(a);
        }

        public AppointmentDAO? GetByPatientName(string patientName)
        {
            var a = _context.Appointments.FirstOrDefault(x => x.PatientName.Equals(patientName, StringComparison.OrdinalIgnoreCase));
            if (a == null) return null;
            return AppointmentMapper.ToDAO(a);
        }

        public AppointmentDAO? GetByDoctorName(string doctorName)
        {
            var a = _context.Appointments.FirstOrDefault(x => x.DoctorName.Equals(doctorName, StringComparison.OrdinalIgnoreCase));
            if (a == null) return null;
            return AppointmentMapper.ToDAO(a);
        }

        public IEnumerable<AppointmentDAO> GetByDateRange(DateTime startDate, DateTime endDate)
        {
            return _context.Appointments
                .Where(a => a.AppointmentDate >= startDate && a.AppointmentDate <= endDate)
                .AsNoTracking()
                .Select(a => AppointmentMapper.ToDAO(a))
                .ToList();
        }
    }
}
