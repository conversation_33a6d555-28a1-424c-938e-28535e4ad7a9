using Modules.Patient.DAO;
using Modules.Patient.DTOs;
using Modules.Patient.Repositories;
using Modules.Patient;

namespace Modules.Patient.Services
{
    public class PatientService : IPatientService
    {
        private readonly IPatientRepository _repository;

        public PatientService(IPatientRepository repository)
        {
            _repository = repository;
        }

        public void Create(PatientCreateDTO dto)
        {
            var dao = PatientMapper.ToDAO(dto);
            _repository.Create(dao);
        }

        public void Update(int id, PatientUpdateDTO dto)
        {
            var dao = PatientMapper.ToDAO(dto);
            dao.Id = id;
            _repository.Update(dao);
        }

        public void Delete(int id)
        {
            _repository.Delete(id);
        }

        public List<PatientRespondDTO> GetAllPatients()
        {
            var daos = _repository.GetAllPatient();
            return PatientMapper.ToRespondDTOList(daos);
        }

        public PatientRespondDTO GetPatientById(int id)
        {
            var dao = _repository.GetPatientById(id);
            return PatientMapper.ToRespondDTO(dao);
        }
    }
}
