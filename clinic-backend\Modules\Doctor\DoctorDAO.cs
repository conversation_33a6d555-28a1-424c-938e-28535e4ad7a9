using System.Collections.Generic;
using System.Linq;
using Modules.Enums;

namespace Modules.Doctor.DAO
{
    public class DoctorDAO
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public required string Specialty { get; set; }
        public required string Phone { get; set; }
        public required string Email { get; set; }
        public string? Department { get; set; }
        public string? PhotoUrl { get; set; }
        public double Rating { get; set; }
        public string? Location { get; set; }
        public string? Experience { get; set; }
        public DoctorAvailability Availability { get; set; } = DoctorAvailability.Busy;
        // BookedSlots and WorkingHours are not included in DAO for simplicity, unless needed for business logic
    }

}
