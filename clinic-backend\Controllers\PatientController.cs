using Microsoft.AspNetCore.Mvc;
using Modules.Patient.Services;
using Modules.Patient.DTOs;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PatientController : ControllerBase
    {
        private readonly IPatientService _service;

        public PatientController(IPatientService service)
        {
            _service = service;
        }

        [HttpGet]
        public ActionResult<IEnumerable<PatientRespondDTO>> GetAll()
        {
            var patients = _service.GetAllPatients();
            return Ok(patients);
        }

        [HttpGet("{id}")]
        public ActionResult<PatientRespondDTO> GetById(int id)
        {
            try
            {
                var patient = _service.GetPatientById(id);
                return Ok(patient);
            }
            catch
            {
                return NotFound(new { warning = $"Patient with id {id} not found." });
            }
        }

        [HttpPost]
        public IActionResult Create([FromBody] PatientCreateDTO dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Name))
            {
                return BadRequest("Name is required.");
            }
            _service.Create(dto);
            return Ok();
        }

        [HttpPut("{id}")]
        public IActionResult Update(int id, [FromBody] PatientUpdateDTO dto)
        {
            try
            {
                _service.Update(id, dto);
                return Ok();
            }
            catch
            {
                return NotFound(new { warning = $"Patient with id {id} not found." });
            }
        }

        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            try
            {
                _service.Delete(id);
                return Ok();
            }
            catch
            {
                return NotFound(new { warning = $"Patient with id {id} not found." });
            }
        }
    }
}