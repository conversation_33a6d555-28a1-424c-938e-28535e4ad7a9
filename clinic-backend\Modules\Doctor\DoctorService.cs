using Modules.Doctor.DTOs;
using Modules.Doctor.Repositories;
using Modules.Doctor.DAO;
using Modules.Doctor;

namespace Modules.Doctor.Services
{
    public class DoctorService : IDoctorService
    {
        private readonly IDoctorRepository _repository;

        public DoctorService(IDoctorRepository repository)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        public DoctorRespondDTO GetDoctorById(int id)
        {
            if (id <= 0)
                throw new ArgumentException("Doctor ID must be greater than 0.", nameof(id));

            var dao = _repository.GetById(id);
            if (dao == null)
                throw new KeyNotFoundException($"Doctor with ID {id} not found.");

            return DoctorMapper.ToRespondDTO(dao);
        }

        public IEnumerable<DoctorRespondDTO> GetAllDoctors()
        {
            var daos = _repository.GetAll();
            return DoctorMapper.ToRespondDTOList(daos);
        }

        public void CreateDoctor(DoctorCreateDTO dto)
        {
            if (dto == null)
                throw new ArgumentNullException(nameof(dto));

            ValidateCreateDTO(dto);

            var dao = DoctorMapper.ToDAO(dto);
            _repository.Add(dao);
        }

        public void UpdateDoctor(DoctorUpdateDTO dto)
        {
            if (dto == null)
                throw new ArgumentNullException(nameof(dto));

            if (dto.Id <= 0)
                throw new ArgumentException("Doctor ID must be greater than 0.", nameof(dto));

            if (!_repository.Exists(dto.Id))
                throw new KeyNotFoundException($"Doctor with ID {dto.Id} not found.");

            ValidateUpdateDTO(dto);

            var dao = DoctorMapper.ToDAO(dto);
            _repository.Update(dao);
        }

        public void DeleteDoctor(int id)
        {
            if (id <= 0)
                throw new ArgumentException("Doctor ID must be greater than 0.", nameof(id));

            if (!_repository.Exists(id))
                throw new KeyNotFoundException($"Doctor with ID {id} not found.");

            _repository.Delete(id);
        }

        public bool DoctorExists(int id)
        {
            return id > 0 && _repository.Exists(id);
        }

        private void ValidateCreateDTO(DoctorCreateDTO dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Name))
                throw new ArgumentException("Doctor name is required.", nameof(dto.Name));

            if (string.IsNullOrWhiteSpace(dto.Specialty))
                throw new ArgumentException("Doctor specialty is required.", nameof(dto.Specialty));
        }

        private void ValidateUpdateDTO(DoctorUpdateDTO dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Name))
                throw new ArgumentException("Doctor name is required.", nameof(dto.Name));

            if (string.IsNullOrWhiteSpace(dto.Specialty))
                throw new ArgumentException("Doctor specialty is required.", nameof(dto.Specialty));
        }
    }
}
