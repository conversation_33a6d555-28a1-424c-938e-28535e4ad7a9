using Modules.Appointment.DTOs;
using Modules.Appointment.Interfaces;


namespace Modules.Appointment.Services
{
    public class AppointmentService : IAppointmentService
    {
        private readonly IAppointmentRepository _repository;

        public AppointmentService(IAppointmentRepository repository)
        {
            _repository = repository;
        }

        public AppointmentRespondDTO GetById(int id)
        {
            var dao = _repository.GetById(id);
            if (dao == null)
                throw new KeyNotFoundException($"Appointment with ID {id} not found.");

            return AppointmentMapper.ToRespondDTO(dao);
        }

        public IEnumerable<AppointmentRespondDTO> GetAll()
        {
            var daos = _repository.GetAll();
            return AppointmentMapper.ToRespondDTOList(daos);
        }

        public void Create(AppointmentCreateDTO dto)
        {
            var dao = AppointmentMapper.ToDAO(dto);
            _repository.Create(dao);
        }

        public void Update(AppointmentUpdateDTO dto)
        {
            var dao = AppointmentMapper.ToDAO(dto);
            _repository.Update(dao);
        }

        public void Delete(int id)
        {
            _repository.Delete(id);
        }

        public IEnumerable<AppointmentRespondDTO> GetByDateRange(DateTime start, DateTime end)
        {
            var daos = _repository.GetByDateRange(start, end);
            return AppointmentMapper.ToRespondDTOList(daos);
        }
    }
}
