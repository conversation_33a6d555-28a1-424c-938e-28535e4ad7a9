import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../Auth/AuthContext';

interface LoginFormData {
    email: string;
    password: string;
}

export const LoginPage: React.FC = () => {
    const [loginType, setLoginType] = useState<'user' | 'admin'>('user');
    const { login, loginAsAdmin, continueAsGuest } = useAuth();
    const [error, setError] = useState('');

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<LoginFormData>();

    const onSubmit = (data: LoginFormData) => {
        setError('');
        const success = loginType === 'admin'
            ? loginAsAdmin(data.email, data.password)
            : login(data.email, data.password);

        if (!success) {
            setError('Invalid credentials. Please try again.');
        }
    };

    return (
        <div>
            Login page
        </div>
    );
};