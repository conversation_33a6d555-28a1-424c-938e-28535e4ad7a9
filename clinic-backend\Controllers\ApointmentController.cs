using Microsoft.AspNetCore.Mvc;
using Modules.Appointment.Interfaces;
using Modules.Appointment.DAO;
using Modules.Appointment.Entities;
using Modules.Appointment.DTOs;
using Modules.Enums;

namespace Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AppointmentController : ControllerBase
    {
        private readonly IAppointmentRepository _repository;

        public AppointmentController(IAppointmentRepository repository)
        {
            _repository = repository;
        }
        //POST
        [HttpPost]
        public IActionResult Create([FromBody] AppointmentCreateDTO dto)
        {
            if (string.IsNullOrWhiteSpace(dto.PatientName) || string.IsNullOrWhiteSpace(dto.DoctorName))
            {
                return BadRequest("PatientN<PERSON> and <PERSON>Name are required.");
            }

            var dao = new AppointmentDAO
            {
                AppointmentDate = dto.AppointmentDate,
                PatientName = dto.PatientName,
                DoctorName = dto.DoctorName,
                Status = AppointmentStatus.Null
            };

            _repository.Create(dao);
            return Ok();
        }
        //PUT
        [HttpPut]
        public IActionResult Update([FromBody] AppointmentUpdateDTO dto)
        {
            if (dto.Id <= 0 || string.IsNullOrWhiteSpace(dto.PatientName) || string.IsNullOrWhiteSpace(dto.DoctorName))
            {
                return BadRequest("PatientName, and DoctorName are required.");
            }

            var dao = new AppointmentDAO
            {
                Id = dto.Id,
                AppointmentDate = dto.AppointmentDate,
                PatientName = dto.PatientName ?? string.Empty,
                DoctorName = dto.DoctorName ?? string.Empty,
                Status = dto.Status ?? AppointmentStatus.Null
            };

            _repository.Update(dao);
            return Ok();
        }
        [HttpGet("{id}")]
        public ActionResult<AppointmentRespondDTO> GetById(int id)
        {
            var dao = _repository.GetById(id);
            if (dao == null)
            {
                return NotFound(new { warning = $"Appointment with id {id} not found." });
            }
            var respondDto = new AppointmentRespondDTO
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName,
                DoctorName = dao.DoctorName,
                Status = dao.Status
            };
            return respondDto;
        }
        //GET
        [HttpGet]
        public ActionResult<IEnumerable<AppointmentRespondDTO>> GetAll()
        {
            var daos = _repository.GetAll();
            var respondDtos = daos.Select(dao => new AppointmentRespondDTO
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName,
                DoctorName = dao.DoctorName,
                Status = dao.Status
            });
            return Ok(respondDtos);
        }


        [HttpGet("byPatientName")]
        public ActionResult<AppointmentRespondDTO> GetByPatientName(string patientName)
        {
            if (string.IsNullOrWhiteSpace(patientName))
            {
                return BadRequest("PatientName is required.");
            }

            var dao = _repository.GetByPatientName(patientName);
            if (dao == null)
            {
                return NotFound(new { warning = $"No appointment found for patient name '{patientName}'." });
            }

            var respondDto = new AppointmentRespondDTO
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName,
                DoctorName = dao.DoctorName,
                Status = dao.Status
            };
            return respondDto;
        }
        [HttpGet("byDoctorName")]
        public ActionResult<AppointmentRespondDTO> GetByDoctorName(string doctorName)
        {
            if (string.IsNullOrWhiteSpace(doctorName))
            {
                return BadRequest("DoctorName is required.");
            }

            var dao = _repository.GetByDoctorName(doctorName);
            if (dao == null)
            {
                return NotFound(new { warning = $"No appointment found for doctor name '{doctorName}'." });
            }

            var respondDto = new AppointmentRespondDTO
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName,
                DoctorName = dao.DoctorName,
                Status = dao.Status
            };
            return respondDto;
        }
        [HttpGet("byDateRange")]
        public ActionResult<IEnumerable<AppointmentRespondDTO>> GetByDateRange(DateTime startDate, DateTime endDate)
        {
            if (startDate > endDate)
            {
                return BadRequest("Start date must be before end date.");
            }

            var daos = _repository.GetByDateRange(startDate, endDate);
            var respondDtos = daos.Select(dao => new AppointmentRespondDTO
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName,
                DoctorName = dao.DoctorName,
                Status = dao.Status
            });
            return Ok(respondDtos);
        }
        //DELETE
        [HttpDelete("{id}")]
        public IActionResult Delete(int id)
        {
            if (id <= 0)
            {
                return BadRequest("Invalid appointment ID.");
            }

            _repository.Delete(id);
            return Ok();
        }

    }



}
