using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Modules.Appointment.Interfaces;
using Modules.Appointment.Repositories;
using Modules.Appointment.Services;
using Modules.Doctor.Interfaces;
using Modules.Doctor.Repositories;
using Modules.Doctor.Services;
using Modules.Patient.Repositories;
using Modules.Patient.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddControllers();
builder.Services.AddDbContext<Data.ClinicDbContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));
/*--------------------------------Uncomment later after finishing Redis setup*--------------------------------------------------------------*/
// Register Redis connection multiplexer

// var redisConnectionString = builder.Configuration.GetConnectionString("Redis")
//     ?? throw new InvalidOperationException("Redis connection string is missing in configuration.");

// builder.Services.AddSingleton<StackExchange.Redis.IConnectionMultiplexer>(
//     StackExchange.Redis.ConnectionMultiplexer.Connect(redisConnectionString)
// );
// builder.Services.AddSingleton<Infrastructure.RedisService>();
// Register the services as scoped dependencies
/*--------------------------------Uncomment later after finishing Redis setup*--------------------------------------------------------------*/
builder.Services.AddScoped<IAppointmentService, AppointmentService>();
builder.Services.AddScoped<IDoctorService, DoctorService>();
builder.Services.AddScoped<IPatientService, PatientService>();

// Register the repositories as singleton dependencies
builder.Services.AddScoped<IAppointmentRepository, AppointmentRepository>();
builder.Services.AddScoped<IDoctorRepository, DoctorRepository>();
builder.Services.AddScoped<IPatientRepository, PatientRepository>();


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.MapGet("/", (HttpContext context) =>
{
    var request = context.Request;
    var baseUrl = $"{request.Scheme}://{request.Host}";
    var swaggerUrl = $"{baseUrl}/swagger";
    var html = $"<html><body><p>Access <a href='{swaggerUrl}'>Swagger UI</a></p></body></html>";
    context.Response.ContentType = "text/html";
    return html;
});

app.MapControllers();

app.Run();
