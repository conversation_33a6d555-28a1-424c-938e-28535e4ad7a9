using StackExchange.Redis;
// WIP
// Needed inject into services/controllers for caching or other Redis operations
// Implement methods for setting, getting, and removing keys, as well as serializing objects to JSON
// and deserializing them back from JSON.
namespace Infrastructure
{
    public class RedisService
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly IDatabase _db;

        public RedisService(IConnectionMultiplexer redis)
        {
            _redis = redis;
            _db = _redis.GetDatabase();
        }

        // Set a string value
        public async Task SetStringAsync(string key, string value)
        {
            await _db.StringSetAsync(key, value);
        }

        // Get a string value
        public async Task<string?> GetStringAsync(string key)
        {
            return await _db.StringGetAsync(key);
        }

        // Remove a key
        public async Task<bool> RemoveKeyAsync(string key)
        {
            return await _db.KeyDeleteAsync(key);
        }

        // Set an object as JSON
        public async Task SetObjectAsync<T>(string key, T obj)
        {
            var json = System.Text.Json.JsonSerializer.Serialize(obj);
            await _db.StringSetAsync(key, json);
        }

        // Get an object from JSON
        public async Task<T?> GetObjectAsync<T>(string key)
        {
            var json = await _db.StringGetAsync(key);
            if (json.IsNullOrEmpty) return default;
            return System.Text.Json.JsonSerializer.Deserialize<T>(json!);
        }
    }
}
