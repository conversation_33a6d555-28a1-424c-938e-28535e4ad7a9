using Modules.Patient.DAO;
using Data;
using Microsoft.EntityFrameworkCore;


namespace Modules.Patient.Repositories
{
    public class PatientRepository : IPatientRepository
    {
        private readonly ClinicDbContext _context;

        public PatientRepository(ClinicDbContext context)
        {
            _context = context;
        }

        public IEnumerable<PatientDAO> GetAllPatient()
        {
            return _context.Patients
                .AsNoTracking()
                .Select(entity => PatientMapper.ToDAO(entity))
                .ToList();
        }

        public PatientDAO GetPatientById(int patientId)
        {
            if (patientId <= 0)
                throw new ArgumentException("Patient ID must be greater than zero.", nameof(patientId));

            var entity = _context.Patients.AsNoTracking().FirstOrDefault(p => p.Id == patientId);
            if (entity == null)
                throw new KeyNotFoundException($"Patient with ID {patientId} not found.");

            return PatientMapper.ToDAO(entity);
        }

        public void Create(PatientDAO patient)
        {
            _context.Patients.Add(PatientMapper.ToEntity(patient));
            _context.SaveChanges();
        }

        public void Update(PatientDAO patient)
        {
            var existing = _context.Patients.Find(patient.Id);
            if (existing == null) return;
            PatientMapper.MapToExistingEntity(patient, existing);
            _context.SaveChanges();
        }

        public void Delete(int id)
        {
            var patient = _context.Patients.Find(id);
            if (patient == null) return;
            _context.Patients.Remove(patient);
            _context.SaveChanges();
        }
    }
}
