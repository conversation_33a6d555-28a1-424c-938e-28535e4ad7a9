export interface User {
    id: string;
    name: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    address: string;
    emergencyContact: string;
    medicalHistory?: string[];
    allergies?: string[];
    currentMedications?: string[];
  }
  
  export interface Guest {
    name: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    address: string;
    emergencyContact: string;
    emergencyContactPhone: string;
    medicalHistory: string;
    allergies: string;
    currentMedications: string;
    insuranceProvider?: string;
    insuranceNumber?: string;
  }
  
  export interface Doctor {
    id: string;
    name: string;
    specialty: string;
    department: string;
    photo: string;
    rating: number;
    availability: 'available' | 'busy' | 'offline';
    location: string;
    experience: string;
    workingHours: {
      [key: string]: { start: string; end: string; } | null;
    };
    bookedSlots: {
      date: string;
      time: string;
    }[];
  }
  
  export interface Appointment {
    id: string;
    patientName: string;
    patientEmail: string;
    doctorId: string;
    doctorName: string;
    date: string;
    time: string;
    reason: string;
    status: 'scheduled' | 'completed' | 'cancelled';
    patientType: 'guest' | 'user';
  }
  
  export interface TimeSlot {
    time: string;
    available: boolean;
    reason?: string;
  }