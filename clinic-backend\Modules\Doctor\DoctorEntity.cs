using System;
using System.ComponentModel.DataAnnotations;
using Modules.Enums;

namespace Modules.Doctor.Entities
{
    public class DoctorEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string Name { get; set; }

        public string Phone { get; set; }

        public string Email { get; set; }

        public string Specialty { get; set; }

        public string Department { get; set; }

        public string PhotoUrl { get; set; }

        public double Rating { get; set; }

        public string Location { get; set; }

        public string Experience { get; set; }

        public ICollection<BookedSlot> BookedSlots { get; set; }

        public ICollection<WorkingHourEntity> WorkingHours { get; set; }
        public DoctorAvailability Availability { get; set; } = DoctorAvailability.Busy;
    }
}
