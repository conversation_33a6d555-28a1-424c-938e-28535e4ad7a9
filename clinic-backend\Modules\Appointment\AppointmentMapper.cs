using Modules.Appointment.DTOs;
using Modules.Appointment.DAO;
using Modules.Appointment.Entities;
using Modules.Enums;

namespace Modules.Appointment
{
    public static class AppointmentMapper
    {
        // ===================== DTOs =====================
        public static AppointmentDAO ToDAO(AppointmentCreateDTO dto)
        {
            if (dto == null) return null!;
            return new AppointmentDAO
            {
                Id = dto.Id,
                AppointmentDate = dto.AppointmentDate,
                PatientName = dto.PatientName ?? string.Empty,
                DoctorName = dto.DoctorName ?? string.Empty,
                Status = dto.Status
            };
        }

        public static AppointmentDAO ToDAO(AppointmentUpdateDTO dto)
        {
            if (dto == null) return null!;
            return new AppointmentDAO
            {
                Id = dto.Id,
                AppointmentDate = dto.AppointmentDate,
                PatientName = dto.PatientName ?? string.Empty,
                DoctorName = dto.DoctorName ?? string.Empty,
                Status = dto.Status ?? AppointmentStatus.Null
            };
        }

        public static AppointmentRespondDTO ToRespondDTO(AppointmentDAO dao)
        {
            if (dao == null) return null!;
            return new AppointmentRespondDTO
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName ?? string.Empty,
                DoctorName = dao.DoctorName ?? string.Empty,
                Status = dao.Status
            };
        }

        public static List<AppointmentRespondDTO> ToRespondDTOList(IEnumerable<AppointmentDAO> daos)
        {
            return daos?.Select(ToRespondDTO).ToList() ?? new List<AppointmentRespondDTO>();
        }

        // ===================== DAO <-> Entity =====================
        public static AppointmentEntity ToEntity(AppointmentDAO dao)
        {
            if (dao == null) return null!;
            return new AppointmentEntity
            {
                Id = dao.Id,
                AppointmentDate = dao.AppointmentDate,
                PatientName = dao.PatientName ?? string.Empty,
                DoctorName = dao.DoctorName ?? string.Empty,
                Status = dao.Status
            };
        }

        public static AppointmentDAO ToDAO(AppointmentEntity entity)
        {
            if (entity == null) return null!;
            return new AppointmentDAO
            {
                Id = entity.Id,
                AppointmentDate = entity.AppointmentDate,
                PatientName = entity.PatientName ?? string.Empty,
                DoctorName = entity.DoctorName ?? string.Empty,
                Status = entity.Status
            };
        }

        public static void MapToExistingEntity(AppointmentDAO dao, AppointmentEntity existing)
        {
            if (dao == null || existing == null) return;
            existing.AppointmentDate = dao.AppointmentDate;
            existing.PatientName = dao.PatientName ?? string.Empty;
            existing.DoctorName = dao.DoctorName ?? string.Empty;
            existing.Status = dao.Status;
        }

        // ===================== Entity -> DTO =====================
        public static AppointmentRespondDTO ToRespondDTO(AppointmentEntity entity)
        {
            if (entity == null) return null!;
            return new AppointmentRespondDTO
            {
                Id = entity.Id,
                AppointmentDate = entity.AppointmentDate,
                PatientName = entity.PatientName ?? string.Empty,
                DoctorName = entity.DoctorName ?? string.Empty,
                Status = entity.Status
            };
        }
    }
}
