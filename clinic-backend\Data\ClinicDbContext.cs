using Microsoft.EntityFrameworkCore;
using Modules.Patient.Entities;
using Modules.Doctor.Entities;
using Modules.Appointment.Entities;

namespace Data
{
    public class ClinicDbContext : DbContext
    {
        public ClinicDbContext(DbContextOptions<ClinicDbContext> options) : base(options) { }

        public DbSet<PatientEntity> Patients { get; set; }
        public DbSet<DoctorEntity> Doctors { get; set; }
        public DbSet<AppointmentEntity> Appointments { get; set; }
    }
}